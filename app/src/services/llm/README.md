# LLM Service Refactoring

This directory contains the refactored LLM service components that break down the monolithic `LLMService` into focused, single-responsibility modules, following the same pattern as the session service.

## Architecture Overview

The refactored LLM service follows a modular architecture with clear separation of concerns:

```
llm/
├── core/                    # Core LLM functionality
│   ├── llm_client.py       # OpenAI client wrapper
│   ├── stream_processor.py # Stream processing logic
│   └── error_handler.py    # Error handling utilities
├── managers/                # Specialized managers
│   ├── tool_manager.py     # Tool registration and management
│   ├── message_manager.py  # Message preparation and history utilities
│   └── response_manager.py # Response generation and streaming
├── tools/                   # Tool implementations
│   ├── tool_registry.py    # Tool registry system
│   ├── web_search_tool.py  # Web search functionality
│   ├── csv_tool.py         # CSV generation
│   └── excel_tool.py       # Excel generation
├── excel/                   # Excel-specific utilities
└── utils/                   # General utilities
```

## Key Components

### Core Components (`core/`)

- **LLMClient**: Wrapper for OpenAI AsyncClient with error handling and configuration
- **StreamProcessor**: Handles streaming responses and tool execution
- **ErrorHandler**: Centralized error handling and error response streaming

### Managers (`managers/`)

- **ToolManager**: Manages tool registration, configuration, and availability
- **MessageManager**: Handles message preparation, chat history utilities, and token counting
- **ResponseManager**: Orchestrates response generation, streaming, and validation

### Main Service

The `LLMService` class acts as the main orchestrator, initializing and coordinating all managers while maintaining a clean public interface for backward compatibility.

## Benefits of This Architecture

1. **Single Responsibility**: Each component has a clear, focused purpose
2. **Separation of Concerns**: Tool management, message handling, and response generation are separated
3. **Testability**: Individual components can be easily unit tested
4. **Maintainability**: Changes to one area don't affect others
5. **Extensibility**: New tools or functionality can be added without modifying existing code
6. **Consistency**: Follows the same pattern as the session service

## Usage

The refactored service maintains the same public interface as before:

```python
from app.src.services.llm_service import LLMService

# Initialize service
llm_service = LLMService(
    search_service=search_service,
    prompt_service=prompt_service
)

# Generate streaming response
async for chunk in llm_service.generate_stream_response(
    request=request,
    chat_history=chat_history,
    max_tokens=4096,
    temperature=0
):
    print(chunk)

# Get available tools
tools = llm_service.get_available_tools()

# Validate requests
is_valid = await llm_service.validate_request(request)
```

## Migration Notes

- The public interface remains unchanged for backward compatibility
- Internal implementation now uses managers for better organization
- Static utility methods are now properly organized in the MessageManager
- Tool registration is handled by the ToolManager
- Response generation is managed by the ResponseManager

This refactoring improves code organization while maintaining full backward compatibility with existing code that uses the LLM service.
