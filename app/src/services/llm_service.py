"""
Consolidated LLM Service with integrated functionality.

This module provides a complete LLM service implementation with all necessary
components integrated into a single file for simplicity and maintainability.
"""

import json
from pathlib import Path
from typing import Any, AsyncGenerator, Callable, Dict, List, Optional

import decouple
from openai import APIError, AsyncOpenAI, AuthenticationError, RateLimitError

from app.src.exceptions.error_code import Cha<PERSON><PERSON><PERSON><PERSON>rCode
from app.src.schemas.chat_sessions import ChatHistoryItem
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService


class LLMClient:
    """
    Wrapper for OpenAI AsyncClient with error handling and configuration.
    """

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """Initialize the LLM client."""
        self.api_key = api_key or decouple.config("OPENAI_API_KEY", default=None)
        self.model = model or decouple.config("MODEL", default="gpt-4o-mini")
        self.client = AsyncOpenAI(api_key=self.api_key)

    async def create_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: int = 4096,
        temperature: float = 0,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: str = "auto",
        stream: bool = False,
    ) -> Any:
        """Create a chat completion with error handling."""
        try:
            kwargs = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }

            if tools:
                kwargs["tools"] = tools
                kwargs["tool_choice"] = tool_choice

            return await self.client.chat.completions.create(**kwargs)

        except APIError:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value
        except RateLimitError:
            raise ChatbotErrorCode.REQUEST_DENIED.value
        except AuthenticationError:
            raise ChatbotErrorCode.AUTHENTICATION_FAILED.value
        except Exception:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value


class MessageManager:
    """Manages message preparation and chat history utilities."""

    @staticmethod
    def should_update_summary(chat_history: List[ChatHistoryItem]) -> bool:
        """Determine if chat history summary should be updated."""
        return len(chat_history) == 0 or len(chat_history) % 20 == 0

    @staticmethod
    def extract_questions(chat_history: List[ChatHistoryItem], current_question: str) -> List[str]:
        """Extract questions from chat history for analysis."""
        if not chat_history:
            return [current_question]

        # Extract questions (assuming they are at even indices in the conversation)
        questions = [msg.content for i, msg in enumerate(chat_history) if i % 2 == 0]
        questions.append(current_question)

        return questions

    def prepare_messages(
        self,
        system_prompt: str,
        chat_history: List[ChatHistoryItem],
        current_request: Any,
    ) -> List[dict]:
        """Prepare messages for LLM API call."""
        messages = [{"role": "system", "content": system_prompt}]

        # Add chat history
        for item in chat_history:
            messages.append({"role": item.role, "content": item.content})

        # Add current user message
        messages.append({"role": "user", "content": current_request.question})

        return messages


class LLMService:
    """
    Consolidated LLM Service with integrated functionality.

    This class provides all LLM operations in a single, maintainable service.
    """

    def __init__(
        self,
        search_service: Optional[SearchService] = None,
        prompt_service: Optional[PromptService] = None
    ):
        """
        Initialize the LLM service.

        Args:
            search_service: Search service for web search functionality
            prompt_service: Prompt service for system prompts
        """
        self.search_service = search_service
        self.prompt_service = prompt_service
        self.llm_client = LLMClient()
        self.message_manager = MessageManager()

    @property
    def openai_client(self):
        """
        Backward compatibility property to access the OpenAI client.

        Returns:
            The underlying OpenAI client
        """
        return self.llm_client.client
    
    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response for the given request and chat history.

        Args:
            request: User request object
            chat_history: List of previous chat messages
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            on_tool_call: Optional callback for tool call notifications

        Yields:
            Streaming response content
        """
        try:
            # Get system prompt
            system_prompt = await self._get_system_prompt()
        except ValueError:
            # If prompt service fails, use a default prompt
            system_prompt = "You are a helpful AI assistant."

        # Prepare messages
        messages = self.message_manager.prepare_messages(
            system_prompt=system_prompt,
            chat_history=chat_history,
            current_request=request,
        )

        try:
            # Create streaming response
            response = await self.llm_client.create_chat_completion(
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
            )

            # Process the stream
            async for chunk in response:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception:
            # Handle errors gracefully
            yield "I apologize, but I'm having trouble generating a response right now. Please try again."
    
    def get_available_tools(self) -> List[str]:
        """
        Get list of available tool names.

        Returns:
            List of available tool names
        """
        # Return basic tools for now
        tools = []
        if self.search_service:
            tools.append("web_search")
        return tools

    def get_tool_configs(self) -> List[dict]:
        """
        Get configuration for all available tools.

        Returns:
            List of tool configurations
        """
        # Return basic tool configs for now
        configs = []
        if self.search_service:
            configs.append({
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "Search the web for information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query"
                            }
                        },
                        "required": ["query"]
                    }
                }
            })
        return configs

    def should_update_summary(self, chat_history: List[ChatHistoryItem]) -> bool:
        """
        Determine if chat history summary should be updated.

        Args:
            chat_history: List of chat history items

        Returns:
            True if summary should be updated, False otherwise
        """
        return MessageManager.should_update_summary(chat_history)

    def extract_questions(self, chat_history: List[ChatHistoryItem], current_question: str) -> List[str]:
        """
        Extract questions from chat history for analysis.

        Args:
            chat_history: List of chat history items
            current_question: Current user question

        Returns:
            List of questions including the current one
        """
        return MessageManager.extract_questions(chat_history, current_question)

    async def _get_system_prompt(self) -> str:
        """
        Get the system prompt from the prompt service.

        Returns:
            System prompt string

        Raises:
            ValueError: If prompt service is not available or fails
        """
        if not self.prompt_service:
            return "You are a helpful AI assistant."

        try:
            prompts = await self.prompt_service._get_prompts()
            if prompts and "introduction" in prompts:
                return prompts["introduction"]
            return "You are a helpful AI assistant."
        except Exception:
            raise ValueError("Failed to get system prompt")
